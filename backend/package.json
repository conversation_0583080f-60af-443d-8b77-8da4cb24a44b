{"name": "form4design-highlevel-proxy", "version": "1.0.0", "description": "Secure backend proxy for HighLevel API integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["highlevel", "proxy", "api", "form4design"], "author": "Form4Design", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "dotenv": "^16.0.3"}, "devDependencies": {"nodemon": "^2.0.22"}, "engines": {"node": ">=16.0.0"}}