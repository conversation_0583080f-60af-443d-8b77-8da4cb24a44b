# HighLevel Integration Guide

This guide explains how to integrate your Form4Design React form with HighLevel using Private Integrations.

## 🚀 Quick Start

### Option 1: Direct Integration (Development Only)
For development and testing, you can integrate directly with HighLevel API:

1. **Set up HighLevel Private Integration:**
   - Go to Settings → Other Settings → Private Integrations
   - Create new integration: "Form4Design Website"
   - Select scopes: `contacts.write`, `contacts.read`, `businesses.read`
   - Copy your Private Integration token

2. **Configure Environment Variables:**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your credentials:
   ```
   VITE_HIGHLEVEL_TOKEN=your_private_integration_token_here
   VITE_HIGHLEVEL_LOCATION_ID=your_location_id_here
   ```

3. **The form is already configured!** Your QuoteForm component now uses the HighLevel integration.

### Option 2: Backend Proxy (Production Recommended)
For production, use the secure backend proxy to protect your API credentials:

1. **Set up the backend:**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   ```

2. **Configure backend environment:**
   Edit `backend/.env`:
   ```
   HIGHLEVEL_TOKEN=your_private_integration_token_here
   HIGHLEVEL_LOCATION_ID=your_location_id_here
   PORT=3001
   FRONTEND_URL=http://localhost:5173
   ```

3. **Start the backend server:**
   ```bash
   npm run dev
   ```

4. **Update frontend to use proxy:**
   In your QuoteForm component, change the import:
   ```typescript
   // Change this:
   import { useHighLevel } from '../hooks/useHighLevel';
   
   // To this:
   import { useHighLevel } from '../hooks/useHighLevelProxy';
   ```

5. **Configure frontend environment:**
   Add to your `.env`:
   ```
   VITE_API_ENDPOINT=http://localhost:3001
   ```

## 📋 Features Implemented

### ✅ Form Integration
- **Real-time validation** with field-level error display
- **Loading states** with spinner during submission
- **Success/error handling** with user-friendly messages
- **Data sanitization** and phone number formatting
- **Controlled form inputs** with proper state management

### ✅ HighLevel Integration
- **Contact creation** via HighLevel API v2.0
- **Custom fields** for project details and lead source
- **Automatic tagging** for lead organization
- **Phone number formatting** for international compatibility
- **Error handling** for API failures

### ✅ Security Features
- **Environment variable protection** for API credentials
- **Rate limiting** on backend proxy (10 requests per 15 minutes)
- **Input validation** and sanitization
- **CORS protection** and security headers
- **Error message sanitization** to prevent information leakage

## 🔧 Configuration Details

### HighLevel API Endpoints
- **Base URL:** `https://services.leadconnectorhq.com`
- **Create Contact:** `POST /contacts/`
- **API Version:** `2021-07-28`

### Required HighLevel Scopes
- `contacts.write` - Create and update contacts
- `contacts.read` - Read contact information (optional)
- `businesses.read` - Access location information

### Form Data Mapping
| Form Field | HighLevel Field | Type | Required |
|------------|----------------|------|----------|
| Full Name | `firstName`, `lastName`, `name` | String | Yes |
| Email | `email` | String | Yes |
| Phone | `phone` | String | No |
| Project Details | Custom Field: `project_details` | String | No |
| Lead Source | Custom Field: `lead_source` | String | Auto |
| Submission Date | Custom Field: `submission_date` | ISO String | Auto |

### Automatic Tags Applied
- `Website Lead`
- `React Form` / `Form4Design`
- `Quote Request`

## 🛠️ Development

### Testing the Integration
1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Test form submission:**
   - Fill out the form with valid data
   - Check browser console for API calls
   - Verify contact creation in HighLevel

3. **Test validation:**
   - Try submitting with invalid email
   - Try submitting without required fields
   - Check error messages display correctly

### Debugging
- **Check browser console** for detailed error messages
- **Monitor network tab** for API request/response details
- **Verify environment variables** are loaded correctly
- **Check HighLevel logs** in your account for API calls

## 🚀 Deployment

### Frontend Deployment (Vercel/Netlify)
1. **Set environment variables** in your deployment platform:
   ```
   VITE_API_ENDPOINT=https://your-backend-domain.com
   ```

2. **Deploy your React app** as usual

### Backend Deployment (Railway/Heroku/DigitalOcean)
1. **Deploy the backend** with environment variables:
   ```
   HIGHLEVEL_TOKEN=your_token
   HIGHLEVEL_LOCATION_ID=your_location_id
   FRONTEND_URL=https://your-frontend-domain.com
   NODE_ENV=production
   ```

2. **Update frontend environment** to point to your deployed backend

## 🔍 Troubleshooting

### Common Issues

**"HighLevel token not configured"**
- Ensure `VITE_HIGHLEVEL_TOKEN` is set in `.env`
- Restart your development server after adding environment variables

**"Failed to create contact: 401 Unauthorized"**
- Check your Private Integration token is correct
- Verify the token has required scopes
- Ensure the token hasn't expired

**"Failed to create contact: 400 Bad Request"**
- Check your location ID is correct
- Verify required fields (firstName, email) are provided
- Check the API payload format

**CORS errors when using backend proxy**
- Ensure `FRONTEND_URL` in backend matches your frontend URL
- Check the backend server is running on the correct port

### Getting Help
1. **Check HighLevel API documentation:** https://highlevel.stoplight.io/
2. **Review browser console** for detailed error messages
3. **Test API calls directly** using tools like Postman
4. **Verify HighLevel account permissions** and integration settings

## 📈 Next Steps

### Potential Enhancements
- **Lead scoring** based on project details
- **Automated follow-up sequences** in HighLevel
- **Integration with calendars** for booking consultations
- **Custom field mapping** for additional form fields
- **Webhook handling** for real-time updates
- **Analytics tracking** for form conversion rates

### HighLevel Workflow Integration
- Set up **automated email sequences** for new leads
- Create **task assignments** for sales team follow-up
- Configure **SMS notifications** for urgent leads
- Set up **pipeline automation** based on lead source
